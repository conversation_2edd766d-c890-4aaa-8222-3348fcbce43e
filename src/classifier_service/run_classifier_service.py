from src.classifier_service.common.classifier_router import route_classifier_by_data_source
from src.classifier_service.common.graph_state import DocumentState
from src.classifier_service.utils.clean_storage_name import extract_blob_name
from src.shared.service_registry import doc_processor, blob_client
from src.shared.utils.logger_config import setup_logger

# TODO update service_name_id for classifier and filling service

logger = setup_logger(__name__)

def execute_classification_pipeline(doc_id: int) -> None:
    """
    Runs the rule-based classification pipeline for a given document ID.

    Args:
        doc_id (int): ID of the document to classify.

    Raises:
        ValueError: If any required step fails (e.g., missing document, classifier failure).
    """
    try:
        logger.info(f"---- STARTING CLASSIFICATION FOR DOCUMENT ID: {doc_id} ----")
        # doc_processor.log_document_service_status(doc_id, service_id=1, status_id=1)
        # logging.info(f"Logged service and status before processing")

        # Fetch document metadata
        email_info = doc_processor.get_document_info(doc_id)
        if not email_info:
            raise ValueError("Failed to retrieve document metadata.")

        document_location = email_info["storage_location"]
        title = email_info["original_name"]
        email_source = email_info.get("metadata", {}).get("source", "unknown")

        logger.info(f"Document Location: {document_location}")
        logger.info(f"Email Source: {email_source}")
        logger.info(f"Document Title: {title}")

        # Get company ID and container/blob info
        company_id = doc_processor.get_company_id_by_document_id(doc_id)
        container_name = f"company-{company_id}"
        blob_name = extract_blob_name(document_location)

        logger.info(f"Company ID: {company_id}")
        logger.info(f"Container: {container_name}, Blob: {blob_name}")

        # Fetch first 5 pages of the document
        doc = blob_client.get_document_preview(container_name, blob_name, num_pages=5)
        if not doc:
            raise ValueError(f"Failed to fetch document from blob: {document_location}")

        logger.info(f"Fetched document from blob storage.")

        # Route to appropriate classifier and run classification
        classifier = route_classifier_by_data_source(email_source)
        initial_state = DocumentState(
            doc_title=title,
            document=doc,
            classification_result=None
        )
        final_state = classifier.invoke(initial_state)
        classification = final_state["classification_result"]

        logger.info(f" Document classified as: {classification}")

        # Persist classification result
        doc_type_id = doc_processor.get_document_type_id_by_doc_type_name(classification)
        doc_processor.log_document_type_id(doc_id, doc_type_id)

        logger.info(f"Document Type ID: {doc_type_id} logged successfully.")

    except Exception as e:
        logger.exception(f" Classification failed for document ID {doc_id}: {e}")
        raise


efile_document_ids = [
    2, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45,
    46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 64, 65, 66, 67,
    68, 69, 70, 71, 76, 77, 83, 84, 85, 87, 124, 125, 126, 127, 128, 129, 130, 131,
    132, 133, 134, 136, 145, 146, 151, 153, 163, 164, 165, 182, 183, 184, 188, 192,
    193, 194, 222, 223, 225, 236, 244, 248, 249, 250, 251, 252, 254, 255, 256, 258,
    259, 260, 267, 274, 275, 276, 277, 279, 280, 281, 282, 293, 294, 295, 296, 297,
    304, 306, 315, 316
]

non_court_document_ids = [
    59, 60, 61, 62, 64, 65, 66, 67,
    68, 69, 70, 71, 76, 77, 83, 84, 85, 87, 124, 125, 126, 127, 128, 129, 130, 131,
    132, 133, 134, 136, 145, 146, 151, 153, 163, 164, 165, 182, 183, 184, 188, 192,
    193, 194, 222, 223, 225, 236, 244, 248, 249, 250, 251, 252, 254, 255, 256, 258,
    259, 260, 267, 274, 275, 276, 277, 279, 280, 281, 282, 293, 294, 295, 296, 297,
    304, 306, 315, 316
]
pacer_document_ids = [168, 169, 170, 171, 172, 173, 174, 175, 212, 234, 235]

# Efile: 268, 243, 23, 24,
# Non-court: 258 - CV, 27, 28, 29
# Image: 362
# Docx: 361
# for doc_id in efile_document_ids:
#     doc = execute_classification_pipeline(doc_id)

# doc = execute_classification_pipeline(27)
