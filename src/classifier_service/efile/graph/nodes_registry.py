from src.classifier_service.common.classifier_node_template import make_classifier_node
from src.classifier_service.common.correction_node.self_correction_node import correction_node_with_double_check
from src.classifier_service.efile.consts.categories_list import final_document_types_efile
from src.classifier_service.efile.prompts.prompt_factory import legal_category_prompt, pleading_type_prompt, \
    proof_of_service_prompt, judgment_type_prompt, affidavit_type_prompt, trial_type_prompt, order_type_prompt, \
    general_notice_type_prompt, process_notice_type_prompt, hearing_notice_type_prompt, \
    discovery_evidence_notice_type_prompt, case_status_notice_type_prompt, general_motion_type_prompt, \
    procedural_motion_type_prompt, depositive_motion_type_prompt, post_trial_motions, subject_matter_motion_type_prompt, \
    general_discovery_type_prompt, interrogatory_discovery_type_prompt, request_for_production_discovery_type_prompt, \
    request_for_admission_discovery_type_prompt
from src.classifier_service.efile.prompts.sub_type_prompts import efile_self_correction_prompt
from src.shared.utils.logger_config import setup_logger

logger = setup_logger(__name__)

# Primary Legal Category
initial_legal_doc_type = make_classifier_node(legal_category_prompt, node_name="------[eFILE INITIAL_LEGAL_DOC_TYPE]-----")

# Pleadings
classify_pleading_type = make_classifier_node(pleading_type_prompt, node_name="------[eFILE CLASSIFY_PLEADING_TYPE]-----")

# Proof of Service
classify_proof_of_service = make_classifier_node(proof_of_service_prompt, node_name="------[eFILE CLASSIFY_PROOF_OF_SERVICE]-----")

# Judgments & Post-Trial
classify_judgment_type = make_classifier_node(judgment_type_prompt, node_name="------[eFILE CLASSIFY_JUDGMENT_TYPE]-----")

# Affidavits & Declarations
classify_affidavit_type = make_classifier_node(affidavit_type_prompt, node_name="------[eFILE CLASSIFY_AFFIDAVIT_TYPE]-----")

# Trial & Pre-Trial
classify_trial_type = make_classifier_node(trial_type_prompt, node_name="------[eFILE CLASSIFY_TRIAL_TYPE]-----")

# Orders & Case Management
classify_order_type = make_classifier_node(order_type_prompt, node_name="------[eFILE CLASSIFY_ORDER_TYPE]-----")


# Specialized sub-type classification nodes
# classify_signed_order_type = make_classifier_node(signed_order_type_prompt, node_name="------[eFILE CLASSIFY_SIGNED_ORDER_TYPE]-----")
# classify_proposed_order_type = make_classifier_node(proposed_order_type_prompt, node_name="------[eFILE CLASSIFY_PROPOSED_ORDER_TYPE]-----")
# classify_report_or_stipulation_type = make_classifier_node(report_or_stipulation_type_prompt, node_name="------[eFILE CLASSIFY_REPORT_OR_STIPULATION_TYPE]-----")
# classify_clerk_record_type = make_classifier_node(clerk_record_type_prompt, node_name="------[eFILE CLASSIFY_CLERK_RECORD_TYPE]-----")


# Notices - General
classify_general_notice_type = make_classifier_node(general_notice_type_prompt, node_name="------[eFILE CLASSIFY_GENERAL_NOTICE_TYPE]-----")

# Notices - Procedural & Appearance
classify_procedural_notice_type = make_classifier_node(process_notice_type_prompt, node_name="------[eFILE CLASSIFY_PROCEDURAL_NOTICE_TYPE]-----")

# Notices - Hearing & Trial
classify_hearing_notice_type = make_classifier_node(hearing_notice_type_prompt, node_name="------[eFILE CLASSIFY_HEARING_NOTICE_TYPE]-----")

# Notices - Discovery & Evidence
classify_discovery_evidence_notice_type = make_classifier_node(discovery_evidence_notice_type_prompt, node_name="------[eFILE CLASSIFY_DISCOVERY_EVIDENCE_NOTICE_TYPE]-----")

# Notices - Case Status, Outcome & Settlement
classify_case_status_notice_type = make_classifier_node(case_status_notice_type_prompt, node_name="------[eFILE CLASSIFY_CASE_STATUS_NOTICE_TYPE]-----")

# Motions - General
classify_general_motion_type = make_classifier_node(general_motion_type_prompt, node_name="------[eFILE CLASSIFY_GENERAL_MOTION_TYPE]-----")

# Motions - Procedural
classify_procedural_motion_type = make_classifier_node(procedural_motion_type_prompt, node_name="------[eFILE CLASSIFY_PROCEDURAL_MOTION_TYPE]-----")

# Motions - Dispositive
classify_dispositive_motion_type = make_classifier_node(depositive_motion_type_prompt, node_name="------[eFILE CLASSIFY_DISPOSITIVE_MOTION_TYPE]-----")

# Motions - Post-Trial
classify_post_trial_motion_type = make_classifier_node(post_trial_motions, node_name="------[eFILE CLASSIFY_POST_TRIAL_MOTION_TYPE]-----")

# Motions - Subject Matter
classify_subject_matter_motion_type = make_classifier_node(subject_matter_motion_type_prompt, node_name="------[eFILE CLASSIFY_SUBJECT_MATTER_MOTION_TYPE]-----")

# Discovery - General
classify_general_discovery_type = make_classifier_node(general_discovery_type_prompt, node_name="------[eFILE CLASSIFY_GENERAL_DISCOVERY_TYPE]-----")

# Discovery - Interrogatories
classify_interrogatory_type = make_classifier_node(interrogatory_discovery_type_prompt, node_name="------[eFILE CLASSIFY_INTERROGATORY_TYPE]-----")

# Discovery - Requests for Production
classify_request_for_production_type = make_classifier_node(request_for_production_discovery_type_prompt, node_name="------[eFILE CLASSIFY_REQUEST_FOR_PRODUCTION_TYPE]-----")

# Discovery - Requests for Admission
classify_request_for_admission_type = make_classifier_node(request_for_admission_discovery_type_prompt, node_name="------[eFILE CLASSIFY_REQUEST_FOR_ADMISSION_TYPE]-----")

# # # Self correction Node
efile_correction_node = correction_node_with_double_check(
    prompt_template=efile_self_correction_prompt,
    categories=final_document_types_efile,
    node_name="EFILE_CORRECTION_NODE"
)