from langgraph.graph import StateGraph

from src.classifier_service.common.graph_state import DocumentState
from src.classifier_service.efile.graph.graph_edges import route_by_rule_based_classification, route_by_legal_category, \
    route_discovery_subtype, route_motion_type, route_notice_type, route_order_type, \
    add_final_node_routing_to_correction
from src.classifier_service.efile.graph.nodes_registry import initial_legal_doc_type, classify_pleading_type, \
    classify_proof_of_service, classify_judgment_type, classify_affidavit_type, classify_trial_type, \
    classify_order_type, classify_general_notice_type, classify_procedural_notice_type, \
    classify_hearing_notice_type, classify_discovery_evidence_notice_type, classify_case_status_notice_type, \
    classify_general_motion_type, classify_procedural_motion_type, classify_dispositive_motion_type, \
    classify_post_trial_motion_type, classify_subject_matter_motion_type, classify_general_discovery_type, \
    classify_interrogatory_type, classify_request_for_production_type, classify_request_for_admission_type, \
    efile_correction_node
from src.classifier_service.efile.rule_based_classifier_engine import classify_document_title

builder = StateGraph(DocumentState)

# Rule-based pre-classifier
builder.add_node("rule_based_classifier", classify_document_title)

# Define nodes
builder.add_node("initial_legal_doc_type", initial_legal_doc_type)

# Pleadings
builder.add_node("classify_pleading_type", classify_pleading_type)

# Proof of Service
builder.add_node("classify_proof_of_service", classify_proof_of_service)

# Judgments & Post-Trial
builder.add_node("classify_judgment_type", classify_judgment_type)

# Affidavits & Declarations
builder.add_node("classify_affidavit_type", classify_affidavit_type)

# Trial & Pre-Trial
builder.add_node("classify_trial_type", classify_trial_type)

# Orders & Case Management
builder.add_node("classify_order_type", classify_order_type)

# Specialized classification nodes
# builder.add_node("classify_signed_order_type", classify_signed_order_type)
# builder.add_node("classify_proposed_order_type", classify_proposed_order_type)
# builder.add_node("classify_report_or_stipulation_type", classify_report_or_stipulation_type)
# builder.add_node("classify_clerk_record_type", classify_clerk_record_type)


# Notices
builder.add_node("classify_general_notice_type", classify_general_notice_type)
builder.add_node("classify_procedural_notice_type", classify_procedural_notice_type)
builder.add_node("classify_hearing_notice_type", classify_hearing_notice_type)
builder.add_node("classify_discovery_evidence_notice_type", classify_discovery_evidence_notice_type)
builder.add_node("classify_case_status_notice_type", classify_case_status_notice_type)

# Motions
builder.add_node("classify_general_motion_type", classify_general_motion_type)
builder.add_node("classify_procedural_motion_type", classify_procedural_motion_type)
builder.add_node("classify_dispositive_motion_type", classify_dispositive_motion_type)
builder.add_node("classify_post_trial_motion_type", classify_post_trial_motion_type)
builder.add_node("classify_subject_matter_motion_type", classify_subject_matter_motion_type)

# Discovery
builder.add_node("classify_general_discovery_type", classify_general_discovery_type)
builder.add_node("classify_interrogatory_type", classify_interrogatory_type)
builder.add_node("classify_request_for_production_type", classify_request_for_production_type)
builder.add_node("classify_request_for_admission_type", classify_request_for_admission_type)

# Self-Correction
builder.add_node("self_correction", efile_correction_node)

# Conditional Routing
builder.add_conditional_edges("rule_based_classifier", route_by_rule_based_classification)
builder.add_conditional_edges("initial_legal_doc_type", route_by_legal_category)
builder.add_conditional_edges("classify_general_discovery_type", route_discovery_subtype)
builder.add_conditional_edges("classify_general_motion_type", route_motion_type)
builder.add_conditional_edges("classify_general_notice_type", route_notice_type)
# builder.add_conditional_edges("classify_order_type", route_order_type)

# Sert entry and finish points
builder.set_entry_point("rule_based_classifier")
# builder.set_finish_point("__end__")
builder.set_finish_point("self_correction")

final_classifier_nodes = [
    "classify_case_status_notice_type",
    "classify_procedural_motion_type",
    "classify_interrogatory_type",
    "classify_request_for_production_type",
    "classify_request_for_admission_type",
    "classify_post_trial_motion_type",
    "classify_dispositive_motion_type",
    "classify_subject_matter_motion_type",
    "classify_affidavit_type",
    "classify_proof_of_service",
    "classify_judgment_type",
    "classify_trial_type",
    "classify_pleading_type",
    "classify_hearing_notice_type",
    "classify_discovery_evidence_notice_type",
    "classify_order_type",
]

""" "classify_report_or_stipulation_type",  "classify_clerk_record_type",  "classify_clerk_record_type",  "classify_proposed_order_type", "classify_signed_order_type", """
# All final nodes route to self-correction
add_final_node_routing_to_correction(builder, final_classifier_nodes)

# Compile the graph
efile_document_classifier_graph = builder.compile()


# # Sample Usage
# initial_state = DocumentState(
#         document="//Users/<USER>/Desktop/fileflow/fileflow.document-classifier/tests_classifier/Doc Types Test/23.03.17. Signed Summons to S&S Site Prep LLC (1).pdf", # Pass the file path here
#         doc_title="23.03.17. Signed Summons to S&S Site Prep LLC (1).pdf",
#         classification_result=None
#     )
#
# final_result_state = efile_document_classifier_graph.invoke(initial_state)
# print(f"Final State: {final_result_state}")