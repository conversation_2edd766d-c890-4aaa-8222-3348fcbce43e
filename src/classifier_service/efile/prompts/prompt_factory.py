from src.classifier_service.common.prompt_generator import generate_category_prompt, generate_enhanced_category_prompt
from src.classifier_service.efile.consts.affidavit_type import AffidavitDocumentType
from src.classifier_service.efile.consts.discovery_types import DiscoveryDocumentType, InterrogatoriesDocumentType, \
    RequestsForProductionDocumentType, RequestsForAdmissionDocumentType
from src.classifier_service.efile.consts.legal_doc_types import LegalDocumentCategory
from src.classifier_service.efile.consts.motion_types import MotionCategory, GeneralProceduralMotionType, \
    DispositiveMotionType, PostTrialMotionType, SubjectMatterReliefMotionType
from src.classifier_service.efile.consts.notice_type import NoticeCategory, ProceduralAppearanceNoticeType, \
    HearingTrialNoticeType, DiscoveryEvidenceNoticeType, CaseStatusNoticeType
from src.classifier_service.efile.consts.order_types import OrdersCaseManagementType
from src.classifier_service.efile.consts.pleading_type import PleadingsDocumentType
from src.classifier_service.efile.consts.post_trial import JudgmentDocumentType
from src.classifier_service.efile.consts.proof_of_service import ServiceDocumentType
from src.classifier_service.efile.consts.trial_doc_type import TrialDocumentType
from src.classifier_service.efile.doc_glossary.orders_case_management_definitions import \
    ORDERS_CASE_MANAGEMENT_KNOWLEDGE_BASE
from src.classifier_service.efile.doc_glossary.pleading_definitions import PLEADING_KNOWLEDGE_BASE
from src.classifier_service.efile.prompts.base_prompt import generate_classification_prompt
from src.classifier_service.efile.prompts.sub_type_prompts import generate_legal_type_classification_prompt, \
    service_classification_prompt, trial_document_type_classification_prompt, \
    order_case_management_classification_prompt, notice_classification_prompt, notice_syb_type_classification_prompt

# Generate prompts for different document categories

# Step 1: Determine General Legal Document Category
legal_category_prompt = generate_category_prompt(generate_legal_type_classification_prompt, LegalDocumentCategory)
# print(legal_category_prompt)

# Step 2: Determine Specific Document Type Based on General Category

#  --------- PLEADINGS ------------
pleading_type_prompt = generate_enhanced_category_prompt(
    generate_classification_prompt,
    PLEADING_KNOWLEDGE_BASE,
    "PleadingsDocumentType",
    include_definitions=True,
    include_usage_notes=True
)
# print(pleading_type_prompt)

#  ----------- PROOF OF SERVICE ------------
proof_of_service_prompt = generate_category_prompt(service_classification_prompt, ServiceDocumentType, "Service of Process & Proof of Service")
# print(proof_of_service_prompt)

# ------------ JUDGMENTS & POST-TRIAL ------------
judgment_type_prompt = generate_category_prompt(generate_classification_prompt, JudgmentDocumentType, "Judgments & Post-Trial")
# print(judgment_type_prompt)

# --------------- AFFIDAVITS & DECLARATIONS ---------------
affidavit_type_prompt = generate_category_prompt(generate_classification_prompt, AffidavitDocumentType, "Affidavits & Declarations (General)")
# print(affidavit_type_prompt)

# ----------- TRIAL & PRE-TRIAL PREPARATIONS ------------
trial_type_prompt = generate_category_prompt(trial_document_type_classification_prompt, TrialDocumentType, "Trial & Pre-Trial Preparations")
# print(trial_type_prompt)


"""confusion between “Proposed Orders” and “Court Orders”

Confusion between “Stipulated Facts” and “Settlement Proposal”

Misclassification between “Signed Order” and “Pretrial Conference Order”"""

# ----------- ORDERS & CASE MANAGEMENT ------------
order_type_prompt = generate_enhanced_category_prompt(
    generate_classification_prompt,
    ORDERS_CASE_MANAGEMENT_KNOWLEDGE_BASE,
    "OrdersCaseManagementType",
    include_definitions=True,
    include_usage_notes=True
)
print(order_type_prompt)

# order_type_prompt = generate_category_prompt(order_case_management_classification_prompt, OrdersCaseManagementType, "Orders & Case Management")
# # print(order_type_prompt)

# signed_order_type_prompt = generate_category_prompt(generate_classification_prompt, SignedOrderType, "Signed Order Types")
# proposed_order_type_prompt = generate_category_prompt(generate_classification_prompt, ProposedOrderType, "Proposed Order Types")
# report_or_stipulation_type_prompt = generate_category_prompt(generate_classification_prompt, ReportOrStipulationType, "Report, Stipulation or Court Record Types")
# clerk_record_type_prompt = generate_category_prompt(generate_classification_prompt, ClerkRecordType, "Clerk Record Types")


#  TODO ---------------- NOTICE -------------------
# General Notice Type
general_notice_type_prompt = generate_category_prompt(notice_classification_prompt, NoticeCategory, "Notice")
# print(general_notice_type_prompt)

# Specific Notice Types
process_notice_type_prompt = generate_category_prompt(notice_syb_type_classification_prompt, ProceduralAppearanceNoticeType, "Procedural & Appearance Notices")
# print(process_notice_type_prompt)

hearing_notice_type_prompt = generate_category_prompt(notice_syb_type_classification_prompt, HearingTrialNoticeType, "Hearing & Trial Related Notices")
# print(hearing_notice_type_prompt)

discovery_evidence_notice_type_prompt = generate_category_prompt(notice_syb_type_classification_prompt, DiscoveryEvidenceNoticeType, "Discovery & Evidence Related Notices")
# print(discovery_evidence_notice_type_prompt)

case_status_notice_type_prompt = generate_category_prompt(notice_syb_type_classification_prompt, CaseStatusNoticeType, "Case Status, Outcome & Settlement Notices")
# print(case_status_notice_type_prompt)


#  ---------- MOTIONS ------------
general_motion_type_prompt = generate_category_prompt(generate_classification_prompt, MotionCategory, "Motion")
# print(general_motion_type_prompt)

procedural_motion_type_prompt = generate_category_prompt(generate_classification_prompt, GeneralProceduralMotionType, "General & Procedural Motions")
# print(procedural_motion_type_prompt)

depositive_motion_type_prompt = generate_category_prompt(generate_classification_prompt, DispositiveMotionType, "Dispositive Motions")
# print(depositive_motion_type_prompt)

post_trial_motions = generate_category_prompt(generate_classification_prompt, PostTrialMotionType, "Post-Trial & Judgment-Related Motions")
# print(post_trial_motions)

subject_matter_motion_type_prompt = generate_category_prompt(generate_classification_prompt, SubjectMatterReliefMotionType, "Specific Subject Matter / Relief Motions (often seen in Family Law, etc.")
# print(subject_matter_motion_type_prompt)

# ----------- DISCOVERY ------------
general_discovery_type_prompt = generate_category_prompt(generate_classification_prompt, DiscoveryDocumentType, "Discovery")
# print(general_discovery_type_prompt)

interrogatory_discovery_type_prompt = generate_category_prompt(generate_classification_prompt, InterrogatoriesDocumentType, "Interrogatories")
# print(interrogatory_discovery_type_prompt)

request_for_production_discovery_type_prompt = generate_category_prompt(generate_classification_prompt, RequestsForProductionDocumentType, "Request for Production of Documents")
# print(request_for_production_discovery_type_prompt)

request_for_admission_discovery_type_prompt = generate_category_prompt(generate_classification_prompt, RequestsForAdmissionDocumentType, "Request for Admission")
# print(request_for_admission_discovery_type_prompt)