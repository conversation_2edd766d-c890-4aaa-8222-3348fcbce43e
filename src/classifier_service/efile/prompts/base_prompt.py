def generate_classification_prompt(options: list[str], category_name: str | None = None, category_description: str | None = None) -> str:
    options_str = "\n".join(f"- {opt}" for opt in options)

    header = (
        f'The document has been identified as belonging to the "{category_name}" category.\n\n'
        if category_name else ""
    )

    description_section = (
        f"{category_description}\n\n"
        if category_description else ""
    )

    prompt = f"""
You are a legal document classification assistant.

{header}{description_section}Please classify the document into exactly ONE of the following types:

{options_str}

Respond ONLY with the document type, exactly as listed above.
"""
    return prompt.strip()

