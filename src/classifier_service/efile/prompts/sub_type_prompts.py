def generate_legal_type_classification_prompt(options: list[str], category_name: str | None = None, category_description: str | None = None) -> str:
    options_str = "\n".join(f"- {opt}" for opt in options)

    header = (
        f'The document has been identified as belonging to the "{category_name}" category.\n\n'
        if category_name else ""
    )

    prompt = f"""
You are a legal document classification assistant.

{header}Please classify the document into exactly ONE of the following types:

{options_str}

*** Important Rules: ***

If the word 'Notice' appears in the title of a document, always classify it as a 'Notice'
If a document title includes terms like "Witness Expert", "Witness List", or similar wording — and it is formatted as a list or a statement of individuals expected to testify at trial —
classify it under: Trial & Pre-Trial Preparations.

If the title does not include “Witness” but includes “Disclosure” —
classify it under: Discovery.

If a word 'Subpoena' appears in the title of a document, always classify it as "Service of Process & Proof of Service" regardless of the subject of Subpoena.

If the title contains words like "Defs", "Initial Discovery Disclosure" it should be classified as Discovery

If the title contains word 'Summons" it should be classified as "Pleadings & Case Initiation"

Respond ONLY with the document type, exactly as listed above.
"""
    return prompt.strip()


def service_classification_prompt(options: list[str], category_name: str | None = None, category_description: str | None = None) -> str:
    options_str = "\n".join(f"- {opt}" for opt in options)

    header = (
        f'The document has been identified as belonging to the "{category_name}" category.\n\n'
        if category_name else ""
    )

    prompt = f"""
You are a legal document classification assistant.

{header}Please classify the document into exactly ONE of the following types:

{options_str}

*** Important Rules: ***

If a word 'Subpoena' appears in the title of a document, always classify it as 'Subpoena', regardless of the document's subject or content.

Respond ONLY with the document type, exactly as listed above.
"""
    return prompt.strip()


def notice_classification_prompt(options: list[str], category_name: str | None = None, category_description: str | None = None) -> str:
    options_str = "\n".join(f"- {opt}" for opt in options)

    header = (
        f'The document has been identified as belonging to the "{category_name}" category.\n\n'
        if category_name else ""
    )

    prompt = f"""
You are a legal document classification assistant.

{header}Please classify the document into exactly ONE of the following types:

{options_str}

If the word 'Cancellation' appears in the title of a document, always classify it as 'Notice of Cancellation', regardless of the document's subject or content.

Use this list to determine which Category the document belongs to, as later on it will be classified into one of the subcategories of the Category you chose:

***

A. Notice 

B. Procedural & Appearance Notices (includes: Notice of Appearance, Notice of Limited Appearance, Notice of Substitution of Counsel, Notice of Withdrawal, Notice of Change of Address, Notice of Designations of Email Address, Notice of Errata, Notice of Joinder, Notice of Supplemental Filing, Notice of Unserved Party, Notice of Confidential Information Within Court Filing, Notice of Assignment of Judge)

C. Hearing & Trial Related Notices (includes: Notice of Hearing, Notice of Hearing Cancellation, Notice of Hearing on Motion to Compel, Notice of Non-Jury Trial, Notice of Readiness for Trial, Notice of Special Set Hearing, Notice of Trial Conflict, Notice of Trial Date, Notice of Unavailability)

D. Discovery & Evidence Related Notices (includes: Notice for Production from Non-Party, Notice of Deposition Duces Tecum, Notice of Expert Witness Disclosure, Notice of Filing Exhibits, Notice of Intent to Rely on Business Records, Notice of Intent to Serve Subpoena, Notice of Intent to Use Expert Testimony, Notice of Issuance of Subpoena, Notice of Rule 30(b)(6) Deposition, Notice of Taking Deposition, Notice to Produce at Trial, Notice to Take Telephonic Deposition, Notice of Compulsory Medical Examination (CME))

E. Case Status, Outcome & Settlement Notices (includes: Notice of Compliance, Notice of Filing Proposed Order, Notice of Filing Return of Service, Notice of Mediation, Notice of No Objection, Notice of Non-Compliance, Notice of Proposed Judgment, Notice of Related Case, Notice of Removal to Federal Court, Notice of Service (general, if not Affidavit of Service), Notice of Settlement Proposal, Notice of Taxation of Costs, Notice of Transfer of Case, Notice of Unopposed Motion, Notice of Voluntary Dismissal, Notice of Withdraw Amended Complaint, Notice to the Court of Settlement)

F. Notice Under Rule 1.525

G. Notice of Filling

H. Notice of Cancellation

I. Notice of Withdrawal (if the word 'Withdrawal' appears in the title, classify it as 'Notice of Withdrawal')

***

Respond ONLY with the document type, exactly as listed above.
"""
    return prompt.strip()


def notice_syb_type_classification_prompt(options: list[str], category_name: str | None = None, category_description: str | None = None) -> str:
    options_str = "\n".join(f"- {opt}" for opt in options)

    header = (
        f'The document has been identified as belonging to the "{category_name}" category.\n\n'
        if category_name else ""
    )

    prompt = f"""
You are a legal document classification assistant.

{header}Please classify the document into exactly ONE of the following types:

{options_str}


If the document doesn't fit any of specified categories, classify it as "Notice".

Respond ONLY with the document type, exactly as listed above.
"""
    return prompt.strip()


def order_case_management_classification_prompt(options: list[str], category_name: str | None = None, category_description: str | None = None) -> str:
    options_str = "\n".join(f"- {opt}" for opt in options)

    header = (
        f'The document has been identified as belonging to the "{category_name}" category.\n\n'
        if category_name else ""
    )

    prompt = f"""
You are a legal document classification assistant.

{header}Please classify the document into exactly ONE of the following types:

{options_str}

*** Step-by-Step Classification Instructions: ***

1. **Check for Judge's Signature:**
   - If the document contains a judge’s handwritten or electronic signature, or text like “Electronically Conformed” followed by a judge’s name, it should be classified as a **Signed Order**.
   - If it is a Signed Order, proceed to determine whether it fits a **specific category** (e.g., Scheduling Order, Trial Order, Case Management Order) or should be labeled simply as **Signed Order** (general).

2. **If the document is NOT signed:**
   - Check if it is a **Proposed Order**:
     - Whenever it says Order Granting/Denying Motion - or Order on Motion and it's not signed it is Proposed Order
     - Look for titles like “Proposed Order” or “Proposed Pretrial Order.”
     - Look for phrases such as “respectfully submitted” or “for the court’s consideration.”
     - It may include signature lines for attorneys but not for the judge.

3. **If the document is not a Proposed or Signed Order:**
   - Determine whether it fits into another specific **Order type**, such as:
     - **Trial Order**: Mentions trial dates, courtroom procedures, or trial-related scheduling.
     - **Scheduling Order**: Sets legal deadlines or case timelines. Is ALWAYS issued BY THE COURT. If the order contains 
     time information but is not issued by the court, it is NOT a Scheduling Order. Most likely Proposed Order.
     - **Case Management Order**: Describes how the case will proceed procedurally.
     if CMO appears in the title it stands for Case Management Order
     
     - **Referral to Mediation**, **Standing Order**, etc.

4. **If the document is not an Order:**
   - Check if it is a **Report, Stipulation, or Court Record**:
     - These documents are not signed by judges and include:
       - **Mediation Reports**
       - **Joint Stipulations**
       - **Settlement Proposals**
       - **Court Minutes**
       - **Clerk’s Entry of Default**

*** Key Hints for Classification: ***
- Use the document’s title and the body text.
- Signature presence is the primary indicator of a Signed Order.
- Language like “Ordered and Adjudged,” “It is hereby ordered,” or judge identifiers point to Signed Orders.
- Look for functional terms like “report,” “stipulation,” or “proposal” to detect non-order documents.

Respond ONLY with the document type, exactly as listed above.
"""
    return prompt.strip()

def trial_document_type_classification_prompt(options: list[str], category_name: str | None = None, category_description: str | None = None) -> str:
    options_str = "\n".join(f"- {opt}" for opt in options)

    header = (
        f'The document has been identified as belonging to the "{category_name}" category.\n\n'
        if category_name else ""
    )

    prompt = f"""
You are a legal document classification assistant.

{header}Please classify the document into exactly ONE of the following types:

{options_str}

*** Important Rules: ***

If a word 'Expert" or 'Disclosure' appears in the title of a document, always classify it as 'Expert Disclosure' even if  words like  Witness List also appear there.
Otherwise if words 'Witness List' and 'Exhibit List' appear in the document title, it should go to 'Witness List' category.
'

Respond ONLY with the document type, exactly as listed above.
"""
    return prompt.strip()


def service_document_type_classification_prompt(options: list[str], category_name: str | None = None, category_description: str | None = None) -> str:
    options_str = "\n".join(f"- {opt}" for opt in options)

    header = (
        f'The document has been identified as belonging to the "{category_name}" category.\n\n'
        if category_name else ""
    )

    prompt = f"""
You are a legal document classification assistant.

{header}Please classify the document into exactly ONE of the following types:

{options_str}

*** Important Rules: ***

If ROS or Return of Service in title ALWAYS classify it as 'Return of Service'.

Respond ONLY with the document type, exactly as listed above.
"""
    return prompt.strip()


def efile_self_correction_prompt(options: list[str], category_name: str | None = None) -> str:
    # options_str = "\n".join(f"- {opt}" for opt in options)

    header = (
        f'The document has been identified as belonging to the "{category_name}" category.\n\n'
        if category_name else ""
    )

    prompt = f"""
You are a legal document classification assistant.

{header} Your task is to verify is the document has been correctly classified into this category.

Steps to Verify:

1. Identify document title
2. Analyze title - does it confirm that the document belongs to the category "{category_name}"?
3. Pay attention not only to the word but to the meaning of the title - does it confirm the category "{category_name}"?
4. Look into the whole docuemnt structure and information - doe sit confirm the category "{category_name}"?

Check if of these:
1. Return, ROS, Returned words are always prioritised - it is Return of Service
2. If it is summons by clerk specify that otherwise just list as - Summons
3. If both Trial and Pre Trial Conference Order appear in the title - Pre Trial Conference Order 
4. Always check if Order is Signed
5. If either Witness list or Exhibit list appear in the title chose one of them. If both appear in the title chose - Witness List
6. Do not mix Trial Order with Scheduling Order
7. If Proposed Order appear in the title - most likely it is Proposed Order - always check for Proposed
8. If Subpoena appear in the title - classify it as Subpoena
9. If Answer appear in the title - classify it as Answer
10. If you see CMC - check if it is Case Management System
11. Be carefully and correctly distinguish between  Defendant and Plaintiff
12. If Notice or Ntc appear in the title - it should always go under Notice or Specific Notice category (e.x. Notice of Deposition, Notice of Cancellation)


If YES, return the document category as it is.

If you believe the document has been misclassified, return the correct category from the list below:

{options}

"""
    return prompt.strip()


def final_correction_prompt(result_1: str, explanation_1: str,result_2,  explanation_2: str) -> str:

    prompt = f"""
You are a legal document classification assistant.

Two previous Agents couldn't agree on which category this document should belong to.

First Agent classified it as "{result_1}" and provided the following explanation: "{explanation_1}".
The second Agent classified it as "{result_2}" and provided the following explanation: "{explanation_2}".

Your task is to verify is the document has been correctly classified into one of these categories.

Return only final, correct classification.

"""
    return prompt.strip()