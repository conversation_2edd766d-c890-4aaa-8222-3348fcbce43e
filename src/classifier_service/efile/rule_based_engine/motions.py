from typing import Optional
from src.classifier_service.efile.rule_based_engine.utils import split_camel_case
from src.shared.utils.logger_config import setup_logger

logger = setup_logger(__name__)

# TODO test after removing "Motion"
MOTION_TITLES = [
    "Motion for Leave",
    "Motion for Change of Venue",
    "Motion for Continuance",
    "Motion for Extension of Time",
    "Motion to Consolidate",
    "Motion to Stay",
    "Motion to Quash",
    "Motion to Strike",
    "Motion to Dismiss",
    "Motion for Summary Judgement",
    "Statement of Undisputed Material Facts",
    "Motion for Directed Verdict",
    "Motion for Judgment as a Matter of Law",
    "Motion to Compel",
    "Motion for Protective Order",
    "Motion in Limine",
    "Motion to Exclude",
    "Motion for Mistrial",
    "Motion for Judgment Notwithstanding the Verdict",
    "Motion for New Trial",
    "Motion for Reconsideration",
    "Motion for Reconsideration of Verdict",
    "Motion to Set Aside Judgment",
    "Motion for Entry of Final Judgment",
    "Motion for Entry of Final Decree",
    "Motion for Attorneys Fees",
    "Motion for Contempt",
    "Motion for Equitable Distribution",
    "Motion for Sanctions",
    "Motion for Spousal Support",
    "Motion for Alimony",
    "Motion for Supervised Visitation",
    "Motion for Temporary Relief",
    "Motion for Temporary Use of Marital Home",
    "Motion for Temporary Use of Marital Property",
    "Motion for Temporary Custody",
    "Motion to Enforce",
    "Motion to Establish Child Support",
    "Motion to Modify Custody",
    "Motion to Relocate",
    "Motion to Terminate Support"
]

def motion_rule_based_motion_classifier(title: str) -> Optional[str]:
    """
    Classifies a legal document title as a motion or specific motion type using keyword matching.

    Args:
        title (str): The document title to classify.

    Returns:
        Optional[str]: The matched motion type (lowercased), or "motion" if general, or None if not matched.
    """
    if not isinstance(title, str) or not title.strip():
        logger.warning("Invalid title passed to motion classifier: %r", title)
        return None

    try:
        title_processed = split_camel_case(title).lower()
    except Exception as e:
        logger.exception("Failed to split camel case on title: %s", title)
        return None

    if "motion" in title_processed:
        for motion_type in sorted(MOTION_TITLES, key=len, reverse=True):
            if motion_type.lower() in title_processed:
                logger.info("Matched motion type: %s", motion_type)
                return motion_type

        logger.info("Detected 'motion' keyword but no specific type matched: %s", title)
        return "motion"

    logger.debug("No motion keyword found in title: %s", title)
    return None

# Sample Usage
# titles = [
# "23.06.13 Plts Motion to Extend Time for Request to Produce ad Answers to Interrogatories",
# "23.06.09 Defs Motion for Enlargement of Time to Respond to Plt Initial Discovery",
# "23.11.15 Plts Motion to Consolidate",
# ]
#
# for t in titles:
#     print(f"{t} → {motion_rule_based_motion_classifier(t)}")