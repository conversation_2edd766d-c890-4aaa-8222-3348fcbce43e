from typing import Optional
from src.classifier_service.efile.rule_based_engine.utils import split_camel_case
from src.shared.utils.logger_config import setup_logger

logger = setup_logger(__name__)


MISC_CLASSIFICATION_TITLES = [
    "Minute Order",
]

def orders_rule_based_misc_classifier(title: str) -> Optional[str]:
    """
    Classifies court orders based on title patterns using rule-based logic.

    Args:
        title (str): The document title to classify.

    Returns:
        Optional[str]: Specific order type if matched, 'order' if general, or None if not recognized.
    """
    if not isinstance(title, str) or not title.strip():
        logger.warning("Invalid title provided to orders classifier: %r", title)
        return None

    try:
        title_processed = split_camel_case(title).lower()
    except Exception as e:
        logger.exception("Failed to process title in orders classifier: %s", title)
        return None

    for doc_type in sorted(MISC_CLASSIFICATION_TITLES, key=len, reverse=True):
        if doc_type.lower() in title_processed:
            logger.info("Matched specific order type: %s", doc_type)
            return doc_type

    if "order" in title_processed:
        logger.info("Generic 'order' detected with no specific match.")
        return "order"

    logger.debug("No order-related classification matched for title: %s", title)
    return None

# Sample Usage
# titles = [
# "23.06.13 Plts Motion to Extend Time for Request to Produce ad Answers to Interrogatories MinuteOrder",
# "23.06.09 Minute Order",
# "23.11.15 Plts Motion to Consolidate",
# ]
#
# for t in titles:
#     print(f"{t} → {orders_rule_based_misc_classifier(t)}")