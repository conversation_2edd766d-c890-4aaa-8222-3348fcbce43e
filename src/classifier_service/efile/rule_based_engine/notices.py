from typing import Optional

from src.classifier_service.efile.rule_based_engine.utils import split_camel_case
from src.shared.utils.logger_config import setup_logger

logger = setup_logger(__name__)

# Flat list of known "Notice" titles
NOTICE_TITLES = [
    "Notice",
    "Notice of Filing",
    "Notice of Appearance",
    "Notice of Limited Appearance",
    "Notice of Substitution of Counsel",
    "Notice of Withdrawal",
    "Notice of Change of Address",
    "Notice of Designations of Email Address",
    "Notice of Errata",
    "Notice of Joinder",
    "Notice of Supplemental Filing",
    "Notice of Unserved Party",
    "Notice of Confidential Information Within Court Filing",
    "Notice of Assignment of Judge",
    "Notice of Hearing",
    "Notice of Hearing Cancellation",
    "Notice of Hearing on Motion to Compel",
    "Notice of Non-Jury Trial",
    "Notice of Readiness for Trial",
    "Notice of Special Set Hearing",
    "Notice of Trial Conflict",
    "Notice of Trial Date",
    "Notice for Production from Non-Party",
    "Notice of Deposition Duces Tecum",
    "Notice of Expert Witness Disclosure",
    "Notice of Filing Exhibits",
    "Notice of Intent to Rely on Business Records",
    "Notice of Intent to Serve Subpoena",
    "Notice of Intent to Use Expert Testimony",
    "Notice of Issuance of Subpoena",
    "Notice of Rule 30(b)(6) Deposition",
    "Notice of Taking Deposition",
    "Notice to Produce at Trial",
    "Notice to Take Telephonic Deposition",
    "Notice of Compliance",
    "Notice of Filing Proposed Order",
    "Notice of Filing Return of Service",
    "Notice of Mediation",
    "Notice of No Objection",
    "Notice of Non-Compliance",
    "Notice of Proposed Judgment",
    "Notice of Related Case",
    "Notice of Removal to Federal Court",
    "Notice of Service",
    "Notice of Settlement Proposal",
    "Notice of Taxation of Costs",
    "Notice of Transfer of Case",
    "Notice of Unopposed Motion",
    "Notice of Voluntary Dismissal",
    "Notice of Withdraw Amended Complaint",
    "Notice to the Court of Settlement",
    "Notice Under Rule 1.525",
    "Ntc",
]

def notice_rule_based_classifier(title: str) -> Optional[str]:
    """
    Classifies a legal document title as a notice or specific notice subtype.

    Args:
        title (str): The document title.

    Returns:
        Optional[str]: The matched notice type (lowercase), "notice" if generic, or None if not a notice.
    """
    if not isinstance(title, str) or not title.strip():
        logger.warning("Invalid title provided to notice classifier: %r", title)
        return None

    try:
        title_processed = split_camel_case(title).lower()
    except Exception as e:
        logger.exception("Failed to process title in notice classifier: %s", title)
        return None

    if "notice" in title_processed or "ntc" in title_processed:
        for notice_type in sorted(NOTICE_TITLES, key=len, reverse=True):
            if notice_type.lower() in title_processed:
                logger.info("Matched notice type: %s", notice_type)
                return notice_type

        logger.info("Generic notice detected with no specific match: %s", title)
        return "notice"

    logger.debug("No notice pattern matched in title: %s", title)
    return None


# Sample Usage
# title = "24.6.11 2nd Amended Notice of CME - Dr. SMith"
# # title = "23.06.27 Def GEICO Notice Of Appearance and Email Designation"
# results = notice_rule_based_classifier(title)
# print(f"Result: {results}")