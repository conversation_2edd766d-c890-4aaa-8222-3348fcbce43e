import re
from typing import Optional

from src.classifier_service.efile.rule_based_engine.utils import split_camel_case
from src.shared.utils.logger_config import setup_logger

logger = setup_logger(__name__)

# Keywords for specific classification
ROS_TITLES = [
    "Return of Service",
    "ros",
    "returned"
]

SOD_TITLES = [
    "Statement of Damages",
    "sod"
]

def ros_sod_classifier(title: str) -> Optional[str]:
    """
    Classifies a document as either a Return of Service (ROS) or Statement of Damages (SOD) based on its title.

    Args:
        title (str): The document title to classify.

    Returns:
        Optional[str]: 'return of service', 'statement of damages', or None if no match found.
    """
    if not isinstance(title, str) or not title.strip():
        logger.warning("Invalid title passed to ros_sod_classifier: %r", title)
        return None

    try:
        title_processed = split_camel_case(title).lower()
    except Exception as e:
        logger.exception("Failed to process title with split_camel_case: %s", title)
        return None

    for ros_term in ROS_TITLES:
        if re.search(rf"\b{re.escape(ros_term)}\b", title_processed):
            logger.info("Matched ROS term: %s", ros_term)
            return "Return of Service"

    for sod_term in SOD_TITLES:
        if re.search(rf"\b{re.escape(sod_term)}\b", title_processed):
            logger.info("Matched SOD term: %s", sod_term)
            return "Statement of Damages"

    logger.debug("No ROS/SOD classification match for title: %s", title)
    return None


# Sample Usage
# test_titles = [
#         "PLEAD - SOD for Def Wundabar Inc - 9.23.24.pdf",
#         "PLEAD - SOD for Def Froyolife - 9.23.24.pdf",
#         "PLEAD - SOD for Def Montrose Verdugo City of Chamber of Commerce - 9.23.24.pdf",
#         "Notice of Returned Summons.pdf",
#         "This has nothing to match.pdf"
# ]
#
# for test in test_titles:
#     result = ros_sod_classifier(test)
#     print(f" {test} →  {result or 'no match'}")
