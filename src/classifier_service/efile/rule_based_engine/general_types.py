from typing import Optional
from src.classifier_service.efile.rule_based_engine.utils import split_camel_case
from src.shared.utils.logger_config import setup_logger

logger = setup_logger(__name__)

MISC_CLASSIFICATION_TITLES = [
    "Affidavit",
    "Affidavit Of Coverage & Employment",
    "Affidavit Of Service",
    "Affidavit Of Service Returned",
    "Amended Answer",
    "Amended Complaint",
    "Appellate Docketing Statement",
    "Case Management Order",
    "Certificate Of Authority Of Mediation",
    "Certificate Of Interested Parties",
    "Citation",
    "Citation Package",
    "Civil Cover Sheet",
    "Clerk's Entry Of Default",
    "Cmo",
    "Court Minutes",
    "Cross Complaint",
    "Default Judgment",
    "Exhibit List",
    "Expert Disclosure",
    "Expert Disclosures",
    "Final Judgment",
    "Interrogatories To Plaintiff",
    "Joint Status Report",
    "Joint Stipulation",
    "Jury Instructions",
    "Mediation Report",
    "Pre-Marked Exhibits",
    "Proposed Findings Of Fact And Conclusions Of Law",
    "Proposed Jury Verdict Form",
    "Proposed Voir Dire",
    "Request For Dismissal",
    "Return Of Service",
    "Ros",
    "Sod",
    "Special Verdict Form",
    "Statement Of Damages",
    "Stip",
    "Stipulated Facts",
    "Stipulation",
    "Subpoena",
    "Subpoena To Produce Documents",
    "Summons",
    "Summons Issued By Clerk",
    "Trial Subpoena",
    "Verdict",
    "Verdict Form",
    "Voir Dire Questions",
    "Witness List"
]


def general_rule_based_misc_classifier(title: str) -> Optional[str]:
    """
    Classifies a document title into a general miscellaneous type using string matching.
    """
    if not isinstance(title, str) or not title.strip():
        logger.warning("Invalid title provided to misc classifier: %r", title)
        return None

    try:
        title_processed = split_camel_case(title).lower()
    except Exception as e:
        logger.exception("Error while processing title with split_camel_case: %s", title)
        return None

    for doc_type in sorted(MISC_CLASSIFICATION_TITLES, key=len, reverse=True):
        if doc_type.lower() in title_processed:
            logger.info("Matched misc document type: %s", doc_type)
            return doc_type

    logger.debug("No misc classification match for title: %s", title)
    return None


# Sample Usage
# titles = [
#     "24.10.1 Def Witness And Exhibit List",
#     "24.11.15 Def GEICO Expert Witness Disclosure",
#     "25.01.31 Amended Complaint E-Filed (1)",
#     "24.6.11 2nd Amended Notice of CME - Dr. SMith",
#     "23.03.24. Verified Return of Service on Def. S&S Site Pre",
#     "23.03.21. Civil Cover Sheet (Auto-Generated)"
# ]
#
# for t in titles:
#     print(f"{t} → {general_rule_based_misc_classifier(t)}")