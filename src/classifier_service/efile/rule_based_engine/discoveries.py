from typing import Optional
from src.classifier_service.efile.rule_based_engine.utils import split_camel_case
from src.shared.utils.logger_config import setup_logger

logger = setup_logger(__name__)

DISCOVERY_TITLES = [
    "Defendant's Initial Disclosures",
    "Interrogatories to Defendant",
    "Interrogatories to Plaintiff",
    "Answers to Interrogatories to Defendant",
    "Answers to Interrogatories to Plaintiff",
    "Requests for Production referencing FRCP",
    "Requests for Production to Defendant",
    "Requests for Production to Plaintiff",
    "Responses to Request for Production to Defendant",
    "Responses to Request for Production to Plaintiff",
    "Requests for Admissions to Defendant",
    "Requests for Admissions to Plaintiff",
    "Responses to Request for Admissions to Defendant",
    "Responses to Request for Admissions to Plaintiff",
    "Expert Disclosures"
]

DISCOVERY_KEYWORDS = [
    "discovery", "interrogatories", "production", "admission", "disclosures"
]

def discovery_rule_based_discovery_classifier(title: str) -> Optional[str]:
    """
    Classifies a legal document title as a discovery-related subtype if it matches known patterns.
    """
    if not isinstance(title, str) or not title.strip():
        logger.warning("Invalid title provided to discovery classifier: %r", title)
        return None

    try:
        title_processed = split_camel_case(title).lower()
    except Exception as e:
        logger.exception("Error while processing title with split_camel_case: %s", title)
        return None

    if any(keyword in title_processed for keyword in DISCOVERY_KEYWORDS):
        for known_title in sorted(DISCOVERY_TITLES, key=len, reverse=True):
            if known_title.lower() in title_processed:
                logger.info("Matched known discovery type: %s", known_title)
                return known_title.lower()

        logger.info("Matched discovery keywords but not a specific title: %s", title)
        return "discovery"

    logger.debug("Title did not match any discovery pattern: %s", title)
    return None