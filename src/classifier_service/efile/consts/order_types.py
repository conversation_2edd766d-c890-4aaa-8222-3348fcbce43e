from enum import Enum

class OrdersCaseManagementType(Enum):
    CASE_MANAGEMENT_ORDER = "Case Management Order"
    COURT_ORDER_REFERRAL_TO_MEDIATION = "Court Order Referral to Mediation"
    DOCKET_CONTROL_ORDER = "Docket Control Order"
    PRETRIAL_CONFERENCE_ORDER = "Pretrial Conference Order"
    SCHEDULING_ORDER = "Scheduling Order"
    SIGNED_ORDER = "Signed Order"
    STANDING_ORDER_OF_THE_JUDGE = "Standing Order of the Judge"
    TRIAL_ORDER = "Trial Order"

    PROPOSED_ORDER = "Proposed Order"
    PROPOSED_PRETRIAL_ORDER = "Proposed Pretrial Order"

    JOINT_STATUS_REPORT = "Joint Status Report"
    JOINT_STIPULATION = "Joint Stipulation"
    MEDIATION_REPORT = "Mediation Report"
    SETTLEMENT_PROPOSAL = "Settlement Proposal"
    STIPULATED_FACTS = "Stipulated Facts"

    COURT_MINUTES = "Court Minutes"

# class OrdersCaseManagementType(Enum):
#     SIGNED_ORDER_TYPE = "Signed Order"
#     PROPOSED_ORDER_TYPE = "Proposed Order"
#     REPORT_OR_STIPULATION_TYPE = "Report, Stipulation or Court Record"
#     CLERK_RECORD_TYPE = "Clerk Record"
#
# class SignedOrderType(Enum):
#     CASE_MANAGEMENT_ORDER = "Case Management Order"
#     COURT_ORDER_REFERRAL_TO_MEDIATION = "Court Order Referral to Mediation"
#     DOCKET_CONTROL_ORDER = "Docket Control Order"
#     PRETRIAL_CONFERENCE_ORDER = "Pretrial Conference Order"
#     SCHEDULING_ORDER = "Scheduling Order"
#     STANDING_ORDER_OF_THE_JUDGE = "Standing Order of the Judge"
#     TRIAL_ORDER = "Trial Order"
#     SIGNED_ORDER_GENERAL = "Signed Order"  # fallback
#
#
# class ProposedOrderType(Enum):
#     PROPOSED_ORDER = "Proposed Order"
#     PROPOSED_PRETRIAL_ORDER = "Proposed Pretrial Order"
#
# class ReportOrStipulationType(Enum):
#     JOINT_STATUS_REPORT = "Joint Status Report"
#     JOINT_STIPULATION = "Joint Stipulation"
#     MEDIATION_REPORT = "Mediation Report"
#     SETTLEMENT_PROPOSAL = "Settlement Proposal"
#     STIPULATED_FACTS = "Stipulated Facts"
#     CERTIFICATE_OF_AUTHORITY_FOR_MEDIATION = "Certificate of Authority for Mediation"  # Added new type
#
#
# class ClerkRecordType(Enum):
#     COURT_MINUTES = "Court Minutes"
#     CLERKS_ENTRY_OF_DEFAULT = "Clerk's Entry of Default"
#
#
