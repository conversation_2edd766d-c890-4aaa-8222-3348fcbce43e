from enum import Enum


# Add default Notice category
class NoticeCategory(Enum):
    # GENERAL_NOTICE = "Notice"
    NOTICE_OF_FILING = "Notice of Filing"
    PROCEDURAL_APPEARANCE_NOTICE = "Procedural & Appearance Notices"
    HEARING_TRIAL_NOTICE = "Hearing & Trial Related Notices"
    DISCOVERY_EVIDENCE_NOTICE = "Discovery & Evidence Related Notices"
    CASE_STATUS_OUTCOME_NOTICE = "Case Status, Outcome & Settlement Notices"
    RULE_1_525_NOTICE = "Notice Under Rule 1.525"
    NOTICE_OF_CANCELLATION = "Notice of Cancellation"
    NOTICE_OF_WITHDRAWAL = "Notice of Withdrawal"


class ProceduralAppearanceNoticeType(Enum):
    NOTICE_OF_APPEARANCE = "Notice of Appearance"
    NOTICE_OF_LIMITED_APPEARANCE = "Notice of Limited Appearance"
    NOTICE_OF_SUBSTITUTION_OF_COUNSEL = "Notice of Substitution of Counsel"
    NOTICE_OF_WITHDRAWAL = "Notice of Withdrawal"
    NOTICE_OF_CHANGE_OF_ADDRESS = "Notice of Change of Address"
    NOTICE_OF_DESIGNATION_OF_EMAIL_ADDRESS = "Notice of Designation of Email Address"
    NOTICE_OF_ERRATA = "Notice of Errata"
    NOTICE_OF_JOINDER = "Notice of Joinder"
    NOTICE_OF_SUPPLEMENTAL_FILING = "Notice of Supplemental Filing"
    NOTICE_OF_UNSERVED_PARTY = "Notice of Unserved Party"
    NOTICE_OF_CONFIDENTIAL_INFORMATION_WITHIN_COURT_FILING = "Notice of Confidential Information Within Court Filing"
    NOTICE_OF_ASSIGNMENT_OF_JUDGE = "Notice of Assignment of Judge"
    NOTICE = "Notice"


class HearingTrialNoticeType(Enum):
    NOTICE_OF_HEARING = "Notice of Hearing"
    # NOTICE_OF_HEARING_CANCELLATION = "Notice of Hearing Cancellation"
    NOTICE_OF_HEARING_ON_MOTION_TO_COMPEL = "Notice of Hearing on Motion to Compel"
    NOTICE_OF_NON_JURY_TRIAL = "Notice of Non-Jury Trial"
    NOTICE_OF_READINESS_FOR_TRIAL = "Notice of Readiness for Trial"
    NOTICE_OF_SPECIAL_SET_HEARING = "Notice of Special Set Hearing"
    NOTICE_OF_TRIAL_CONFLICT = "Notice of Trial Conflict"
    NOTICE_OF_TRIAL_DATE = "Notice of Trial Date"
    NOTICE_OF_UNAVAILABILITY = "Notice of Unavailability"
    NOTICE_OF_TRIAL = "Notice of Trial"  # Added for completeness, not in original list
    NOTICE = "Notice"

class DiscoveryEvidenceNoticeType(Enum):
    NOTICE_FOR_PRODUCTION_FROM_NON_PARTY = "Notice for Production from Non-Party"  # added for completeness
    NOTICE_OF_DEPOSITION_DUCES_TECUM = "Notice of Deposition Duces Tecum"
    NOTICE_OF_EXPERT_WITNESS_DISCLOSURE = "Notice of Expert Witness Disclosure"
    NOTICE_OF_FILING_EXHIBITS = "Notice of Filing Exhibits"
    NOTICE_OF_INTENT_TO_RELY_ON_BUSINESS_RECORDS = "Notice of Intent to Rely on Business Records"
    NOTICE_OF_INTENT_TO_SERVE_SUBPOENA = "Notice of Intent to Serve Subpoena"
    NOTICE_OF_INTENT_TO_USE_EXPERT_TESTIMONY = "Notice of Intent to Use Expert Testimony"
    NOTICE_OF_ISSUANCE_OF_SUBPOENA = "Notice of Issuance of Subpoena"
    NOTICE_OF_RULE_30B6_DEPOSITION = "Notice of Rule 30(b)(6) Deposition"
    NOTICE_OF_TAKING_DEPOSITION = "Notice of Taking Deposition"
    NOTICE_TO_PRODUCE_AT_TRIAL = "Notice to Produce at Trial"
    NOTICE_TO_TAKE_TELEPHONIC_DEPOSITION = "Notice to Take Telephonic Deposition"
    NOTICE_OF_CME = "Notice of Compulsory Medical Examination (CME)"    # Add this one, not is the original list
    NOTICE = "Notice"


class CaseStatusNoticeType(Enum):
    NOTICE_OF_COMPLIANCE = "Notice of Compliance"
    NOTICE_OF_FILING_PROPOSED_ORDER = "Notice of Filing Proposed Order"
    NOTICE_OF_FILING_RETURN_OF_SERVICE = "Notice of Filing Return of Service"
    NOTICE_OF_MEDIATION = "Notice of Mediation"
    NOTICE_OF_NO_OBJECTION = "Notice of No Objection"
    NOTICE_OF_NON_COMPLIANCE = "Notice of Non-Compliance"
    NOTICE_OF_PROPOSED_JUDGMENT = "Notice of Proposed Judgment"
    NOTICE_OF_RELATED_CASE = "Notice of Related Case"
    NOTICE_OF_REMOVAL_TO_FEDERAL_COURT = "Notice of Removal to Federal Court"
    NOTICE_OF_SERVICE = "Notice of Service"
    NOTICE_OF_SETTLEMENT_PROPOSAL = "Notice of Settlement Proposal"
    NOTICE_OF_TAXATION_OF_COSTS = "Notice of Taxation of Costs"
    NOTICE_OF_TRANSFER_OF_CASE = "Notice of Transfer of Case"
    NOTICE_OF_UNOPPOSED_MOTION = "Notice of Unopposed Motion"
    NOTICE_OF_VOLUNTARY_DISMISSAL = "Notice of Voluntary Dismissal"
    NOTICE_OF_WITHDRAW_AMENDED_COMPLAINT = "Notice of Withdraw Amended Complaint"
    NOTICE_TO_THE_COURT_OF_SETTLEMENT = "Notice to the Court of Settlement"
    NOTICE = "Notice"
