from src.classifier_service.common.classifier_node_template import make_classifier_node
from src.classifier_service.common.correction_node.self_correction_node import correction_node_with_double_check
from src.classifier_service.non_court_docs.consts.non_court_all_categories import non_court_categories
from src.classifier_service.non_court_docs.prompts.base_prompts import non_court_self_correction_prompt
from src.classifier_service.non_court_docs.prompts.prompt_factory import general_type_classification_prompt, \
    financial_type_classification_prompt, insurance_type_classification_prompt, medical_type_classification_prompt, \
    government_agency_classification_prompt, case_claim_management_classification_prompt, \
    general_communication_admin_classification_prompt, banking_and_accounting_classification_prompt, \
    loan_and_credits_type_classification_prompt, investment_and_retirement_classification_prompt, \
    income_nad_benefits_classification_prompt, billing_invoices_payments_classification_prompt, \
    property_and_asset_management_classification_prompt, \
    gov_benefit_administration_classification_prompt, medical_records_evaluation_prompt, \
    healthcare_finance_claims_prompt, client_profile_classification_prompt, case_management_classification_prompt, \
    case_resolution_classification_prompt, formal_legal_admin_prompt, non_court_notice_classification_prompt
from src.shared.utils.logger_config import setup_logger

logger = setup_logger(__name__)


# Primary Legal Category
general_type_classifier = make_classifier_node(general_type_classification_prompt, node_name=" ------[NON_COURT INITIAL_DOC_TYPE]-----")

# Subcategories
financial_type_classifier = make_classifier_node(
    financial_type_classification_prompt,
    node_name="------[NON_COURT FINANCIAL_DOC_TYPE]-----"
)
insurance_type_classifier = make_classifier_node(
    insurance_type_classification_prompt,
    node_name="------[NON_COURT INSURANCE_DOC_TYPE]-----"
)
medical_type_classifier = make_classifier_node(
    medical_type_classification_prompt,
    node_name="------[NON_COURT MEDICAL_DOC_TYPE]-----"
)
government_agency_classifier = make_classifier_node(
    government_agency_classification_prompt,
    node_name="------[NON_COURT GOVERNMENT_AGENCY_DOC_TYPE]-----"
)
case_claim_management_classifier = make_classifier_node(
    case_claim_management_classification_prompt,
    node_name="------[NON_COURT CASE_CLAIM_MANAGEMENT_DOC_TYPE]-----"
)
general_correspondence_classifier = make_classifier_node(
    general_communication_admin_classification_prompt,
    node_name="------[NON_COURT GENERAL_COMMUNICATION_ADMIN_DOC_TYPE]-----"
)
# Financial and Banking Document Classification Nodes
banking_and_accounting_classifier = make_classifier_node(banking_and_accounting_classification_prompt, node_name="------[NON_COURT BANKING_AND_ACCOUNTING_DOC_TYPE]-----")
loan_and_credits_classifier = make_classifier_node(loan_and_credits_type_classification_prompt, node_name="------[NON_COURT LOANS_AND_CREDITS_DOC_TYPE  ")
investment_and_retirement_classifier = make_classifier_node(investment_and_retirement_classification_prompt, node_name="------[NON_COURT INVESTMENT_AND_RETIREMENT_DOC_TYPE]-----")
income_and_benefits_classifier = make_classifier_node(income_nad_benefits_classification_prompt, node_name="------[NON_COURT INCOME_AND_BENEFITS_DOC_TYPE]-----")
billing_invoices_payments_classifier = make_classifier_node(billing_invoices_payments_classification_prompt , node_name="------[NON_COURT BILLING_INVOICES_PAYMENTS_DOC_TYPE]-----")
property_and_asset_management_classifier = make_classifier_node(property_and_asset_management_classification_prompt, node_name="------[NON_COURT PROPERTY_AND_ASSET_MANAGEMENT_DOC_TYPE]-----")


# # Insurance Types
# policy_details_and_coverage_type_classifier = make_classifier_node(
#     policy_details_and_coverage_classification_prompt,
#     node_name="------[NON_COURT POLICY_DETAILS_AND_COVERAGE_TYPE]-----"
# )
# claims_process_and_communication_classifier = make_classifier_node(claims_and_communication, node_name="------[NON_COURT CLAIMS_PROCESS_AND_COMMUNICATION_TYPE]-----")

# Medical types
gov_benefit_administration_classifier = make_classifier_node(gov_benefit_administration_classification_prompt, node_name="------[NON_COURT GOVERNMENT_BENEFIT_ADMINISTRATION_DOC_TYPE]-----")
medical_records_evaluation_classifier = make_classifier_node(medical_records_evaluation_prompt, node_name="------[NON_COURT MEDICAL_RECORDS_EVALUATION_DOC_TYPE]-----")
healthcare_finance_claims_classifier = make_classifier_node(healthcare_finance_claims_prompt , node_name="------[NON_COURT HEALTHCARE_FINANCE_CLAIMS_DOC_TYPE]-----")

# Case and Claim Management
client_profile_classifier = make_classifier_node(client_profile_classification_prompt, node_name="------[NON_COURT CLIENT_PROFILE_DOC_TYPE]-----")
case_management_classifier = make_classifier_node(case_management_classification_prompt, node_name="------[NON_COURT CASE_MANAGEMENT_DOC_TYPE]-----")
case_resolution_classifier = make_classifier_node(case_resolution_classification_prompt, node_name="------[NON_COURT CASE_RESOLUTION_DOC_TYPE]-----")
formal_legal_admin_classifier = make_classifier_node(formal_legal_admin_prompt, node_name="------[NON_COURT FORMAL_LEGAL_ADMIN_DOC_TYPE]-----")

# Notice
notice_classifier = make_classifier_node(non_court_notice_classification_prompt, node_name="------[NON_COURT NOTICE_DOC_TYPE]-----")

# Self-correction Node
non_court_correction_node = correction_node_with_double_check(
    prompt_template=non_court_self_correction_prompt,
    categories=non_court_categories,
    node_name="NON_COURT_CORRECTION_NODE"
)