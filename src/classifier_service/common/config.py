from settings import GEMINI_MODEL_NAME_FINAL_CLASSIFIER
from src.classifier_service.common.document_classifier_agent import DocumentClassifier
from src.shared.gemini_client import GeminiService
from src.shared.service_registry import gemini_client

# Document classifier
doc_classifier = DocumentClassifier(gemini=gemini_client)

gemini_client_classifier = GeminiService(model_name=GEMINI_MODEL_NAME_FINAL_CLASSIFIER)
doc_classifier_final = DocumentClassifier(gemini=gemini_client_classifier)