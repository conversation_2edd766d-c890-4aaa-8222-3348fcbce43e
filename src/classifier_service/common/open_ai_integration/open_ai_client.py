import instructor
from fastapi import <PERSON><PERSON>PException
from openai import AzureOpenAI
from pydantic import BaseModel
from src.shared.utils.logger_config import setup_logger

logger = setup_logger(__name__)


class GPTAgent:
    def __init__(
        self,
        apim_key,
        apim_endpoint,
        deployment_name,
        api_version):

        self.apim_key = apim_key
        self.apim_endpoint = apim_endpoint
        self.deployment_name = deployment_name
        self.api_version = api_version

        if not all([self.apim_key, self.apim_endpoint, self.deployment_name]):
            raise ValueError(
                "Azure OpenAI key, endpoint, and deployment name must be provided."
            )

        self.headers = {
            "Content-Type": "application/json",
            "api-key": self.apim_key,
        }

        self.custom_endpoint = self._get_endpoint(self.apim_endpoint)
        self.request_url = f"{self.apim_endpoint}/deployments/{self.deployment_name}/chat/completions?api-version={self.api_version}"

        self.openai_client = AzureOpenAI(
            api_key=self.apim_key,
            api_version=self.api_version,
            azure_endpoint=self.custom_endpoint,
        )
        self.client = instructor.from_openai(self.openai_client)

    def _get_endpoint(self, endpoint):
        """Remove 'openai/' from the endpoint if present."""
        if endpoint.endswith("/openai"):
            return endpoint.rsplit("/openai", 1)[0]
        return endpoint

    def send_request_with_schema(self, prompt, document, doc_type, all_categories, model):
        """
        Send a request with a schema using the specified model.
        """
        if isinstance(model, BaseModel):
            model = model.dict()  # Convert instance to JSON serializable format
        try:
            messages = prompt(document, doc_type, all_categories)
            return self.client.chat.completions.create(
                model=self.deployment_name,
                response_model=model,
                messages=messages,
            )
        except Exception as e:
            logger.error(f"Error during request with schema: {e}")
            raise HTTPException(
                status_code=500, detail=f"Schema request failed: {str(e)}"
            )