def efile_correction_prompt(document, doc_type, all_categories):
    # options_str = ', '.join([cat.value for cat in all_categories])

    system_message = {
        "role": "system",
        "content": f"""You are an intelligent assistant that classifies legal documents into the most relevant categories.

This document has been classified by another Agent as "{doc_type}".
Your task is to carefully analyze the document and determine whether the classification is correct.

Check if of these:
1. Return, ROS, Returned words are always prioritised - it is Return of Service
2. If it is summons by clerk specify that otherwise just list as - Summons
3. If both Trial and Pre Trial Conference Order appear in the title - Pre Trial Conference Order 
4. Always check if Order is Signed
5. If either Witness list or Exhibit list appear in the title chose one of them. If both appear in the title chose - Witness List
6. Do not mix Trial Order with Scheduling Order
7. If Proposed Order appear in the title - most likely it is Proposed Order - always check for Proposed
8. If Subpoena appear in the title - classify it as Subpoena
9. If Answer appear in the title - classify it as Answer
10. If you see CMC - check if it is Case Management System
11. Be carefully and correctly distinguish between  Defendant and Plaintiff
12. If Notice or Ntc appear in the title - it should always go under Notice or Specific Notice category (e.x. Notice of Deposition, Notice of Cancellation)


If YES, return the doc_type exactly as it is and briefly explain why.

If you believe the classification is incorrect, return the most appropriate category from this list:
{all_categories}

Also provide a brief explanation for your choice."""
    }

    user_message = {
        "role": "user",
        "content": f"### Document:\n{document}\n\nDoc Type: {doc_type}"
    }

    return [system_message, user_message]


def non_court_correction_prompt(document, doc_type, all_categories):
    # options_str = ', '.join([cat.value for cat in all_categories])

    system_message = {
        "role": "system",
        "content": f"""You are an intelligent assistant that classifies legal documents into the most relevant categories.

This document has been classified by another Agent as "{doc_type}".
Your task is to carefully analyze the document and determine whether the classification is correct.

If YES, return the doc_type exactly as it is and briefly explain why.

If you believe the classification is incorrect, return the most appropriate category from this list:
{all_categories}

Also provide a brief explanation for your choice."""
    }

    user_message = {
        "role": "user",
        "content": f"### Document:\n{document}\n\nDoc Type: {doc_type}"
    }

    return [system_message, user_message]

def pacer_correction_prompt(document, doc_type, all_categories):
    # options_str = ', '.join([cat.value for cat in all_categories])

    system_message = {
        "role": "system",
        "content": f"""You are an intelligent assistant that classifies legal documents into the most relevant categories.

This document has been classified by another Agent as "{doc_type}".
Your task is to carefully analyze the document and determine whether the classification is correct.

If YES, return the doc_type exactly as it is and briefly explain why.

If you believe the classification is incorrect, return the most appropriate category from this list:
{all_categories}

Also provide a brief explanation for your choice."""
    }

    user_message = {
        "role": "user",
        "content": f"### Document:\n{document}\n\nDoc Type: {doc_type}"
    }

    return [system_message, user_message]