from src.classifier_service.common.open_ai_integration.config import gpt_client
from src.classifier_service.common.correction_node.response_schema import ClassificationCorrectionResponse
from src.classifier_service.common.open_ai_integration.utils import extract_pdf_text
from src.classifier_service.pacer.consts import all_categories_pacer

file_path = ""

def correction_sample():
    document_text = extract_pdf_text(file_path)
    doc_type = "Civil Cover Sheet"  # previous classification

    response = gpt_client.send_request_with_schema(
        document=document_text,
        doc_type=doc_type,
        all_categories=all_categories,
        model=ClassificationCorrectionResponse
    )

    print("Model response:", response)

# Run it
if __name__ == "__main__":
    correction_sample()
