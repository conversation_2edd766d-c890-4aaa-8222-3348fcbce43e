from io import Bytes<PERSON>
from typing import Union
from PyPDF2 import PdfReader
from src.shared.utils.logger_config import setup_logger

logger = setup_logger(__name__)

# Helper function used to extract text from PDFs for testing and prototyping only, in prod OCR should be used
def extract_pdf_text(file_input: Union[str, bytes], max_pages: int = 5) -> str:
    """
    Extracts text from the first `max_pages` of a PDF.
    Accepts either a file path (str) or PDF content as bytes.
    """
    try:
        # Handle string path or bytes input
        if isinstance(file_input, str):
            reader = PdfReader(file_input)
        elif isinstance(file_input, bytes):
            reader = PdfReader(BytesIO(file_input))
        else:
            raise ValueError("file_input must be a file path (str) or bytes")

        extracted_text = []

        for i, page in enumerate(reader.pages):
            if i >= max_pages:
                break
            try:
                text = page.extract_text()
                if text:
                    extracted_text.append(text)
            except Exception as e:
                logger.warning(f" Failed to extract text from page {i}: {e}")

        return "\n".join(extracted_text)

    except Exception as e:
        logger.error(f" Failed to extract PDF text: {e}")
        return ""

