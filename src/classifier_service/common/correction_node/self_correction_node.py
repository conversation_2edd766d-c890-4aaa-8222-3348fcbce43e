from src.classifier_service.common.config import doc_classifier, doc_classifier_final
from src.classifier_service.common.correction_node.response_schema import response_model
from src.classifier_service.common.prompt_generator import generate_correction_prompt
from src.classifier_service.efile.prompts.sub_type_prompts import final_correction_prompt
from src.shared.utils.logger_config import setup_logger

logger = setup_logger(__name__)

def run_double_check_classification(state, prompt_template, categories):
    if not isinstance(state, dict) or "document" not in state:
        raise TypeError("State must be a dictionary with a 'document' key")

    logger.info(f"--------[RUNNING DOUBLE CHECK CLASSIFICATION]----------")

    doc_type = state.get("classification_result", "Unknown")
    prompt = generate_correction_prompt(prompt_template, categories, state)

    # --- First classification ---
    try:
        logger.info("🔧 Generating classification 1...")
        response_1 = doc_classifier.classify(
            state["document"],
            prompt,
            state['doc_title'],
            schema_type=response_model
        )
        logger.debug(f"✅ Response 1: {response_1}")
    except Exception as e:
        logger.exception(f"❌ Error during first classification: {e}")
        return state

    # --- Second classification ---
    try:
        logger.info("🔧 Generating classification 2...")
        response_2 = doc_classifier.classify(
            state["document"],
            prompt,
            state['doc_title'],
            schema_type=response_model
        )
        logger.debug(f"✅ Response 2: {response_2}")
    except Exception as e:
        logger.exception(f"❌ Error during second classification: {e}")
        return state

    # Unwrap lists if needed
    if isinstance(response_1, list) and len(response_1) > 0:
        response_1 = response_1[0]
    if isinstance(response_2, list) and len(response_2) > 0:
        response_2 = response_2[0]

    result_1, explanation_1 = response_1.result, response_1.explanation
    result_2, explanation_2 = response_2.result, response_2.explanation

    state.update({
        'explanation_1': explanation_1,
        'explanation_2': explanation_2,
        'classifier_1': result_1,
        'classifier_2': result_2,
    })

    logger.info(f"🧠 Classifier 1 result: {result_1}")
    logger.info(f"🧠 Classifier 2 result: {result_2}")

    if result_1.strip().lower() == result_2.strip().lower():
        logger.info("✅ Classifiers agree.")
        return {**state, "classification_result": result_1}

    logger.warning("⚠️ Classifiers disagree. Triggering final correction...")

    combined_prompt = final_correction_prompt(
        result_1,
        explanation_1,
        result_2,
        explanation_2
    )

    try:
        final_response = doc_classifier_final.classify(
            state["document"],
            combined_prompt,
            state['doc_title']
        )
        state['final_classification'] = final_response
        logger.info(f"🧠 Final resolved classification: {final_response}")
        return {**state, "classification_result": final_response}
    except Exception as e:
        logger.exception("❌ Error during final correction classification")
        return state

def correction_node_with_double_check(prompt_template, categories, node_name="CORRECTION_NODE"):
    def node(state):
        return run_double_check_classification(state, prompt_template, categories)
    return node
