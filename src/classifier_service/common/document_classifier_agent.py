from pathlib import Path
from PyPDF2 import  PdfWriter
import tempfile
from src.shared.utils.logger_config import setup_logger

logger = setup_logger(__name__)


class DocumentClassifier:
    def __init__(self, gemini):
        self.gemini = gemini

    def classify(self, document_input, prompt: str, original_filename: str, schema_type=None) -> str:
        try:
            if isinstance(document_input, bytes):
                logger.debug("[Classifier] Input is bytes — routing to _classify_from_bytes")
                return self._classify_from_bytes(document_input, prompt, original_filename, schema_type)

            elif isinstance(document_input, (str, Path)):
                logger.debug("[Classifier] Input is file path — routing to _classify_from_file")
                return self._classify_from_file(Path(document_input), prompt, schema_type)

            raise TypeError("Unsupported input type for classification (expected bytes, str, or Path)")

        except Exception as e:
            logger.error("[Classifier] Classification failed", exc_info=True)
            return "Unknown"

    def _classify_from_file(self, file_path: Path, prompt: str, schema_type=None) -> str:
        if not file_path.exists():
            logger.error(f"[File Classify] File not found: {file_path}")
            return "Unknown"

        try:
            logger.debug(f"[File Classify] Sending file to Gemini: {file_path}")
            result = self.gemini.generate_from_file(
                prompt=prompt,
                file_path=file_path,
                schema_type=schema_type
            )
            logger.info(f"Classification result: {result}")
            return result

        except Exception as e:
            logger.error("[File Classify] Failed to classify file", exc_info=True)
            return "Unknown"

    def _classify_from_bytes(self, file_bytes: bytes, prompt: str, original_filename: str, schema_type=None) -> str:
        ext = Path(original_filename).suffix.lower()

        try:
            temp_path = self._write_temp_file(file_bytes, suffix=ext or ".bin")

            logger.debug(f"[Bytes Classify] Sending file to Gemini: {original_filename} ({ext})")
            result = self.gemini.generate_from_file(
                prompt=prompt,
                file_path=temp_path,
                schema_type=schema_type
            )
            logger.info(f"Classification result: {result}")
            return result

        except Exception as e:
            logger.error("[Bytes Classify] Failed to classify file from bytes", exc_info=True)
            return "Unknown"

    def _write_temp_file(self, data, suffix: str) -> Path:
        """
        Write binary data or a PdfWriter to a temporary file.
        Returns the Path to the created file.
        """
        try:
            with tempfile.NamedTemporaryFile(delete=False, suffix=suffix) as tmp:
                if isinstance(data, PdfWriter):
                    data.write(tmp)
                else:
                    tmp.write(data)
                tmp.flush()
                logger.debug(f"[Temp File] Created temporary file: {tmp.name}")
                return Path(tmp.name)
        except Exception as e:
            logger.error("[Temp File] Failed to create temporary file", exc_info=True)
            raise