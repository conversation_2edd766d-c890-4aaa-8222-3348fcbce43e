import os
import subprocess
from io import BytesIO
from pathlib import Path
from PyPDF2 import Pdf<PERSON><PERSON><PERSON>, PdfWriter
import tempfile
from PyPDF2.generic import NullObject
from src.shared.utils.logger_config import setup_logger

logger = setup_logger(__name__)

class DocumentClassifier:
    def __init__(self, gemini):
        self.gemini = gemini

    def extract_text_from_pdf_bytes(self, pdf_bytes: bytes, max_pages: int = 5) -> str:
        """
        Extract text from PDF bytes for text-based classification.
        """
        try:
            reader = PdfReader(BytesIO(pdf_bytes))
            extracted_text = []

            for i, page in enumerate(reader.pages):
                if i >= max_pages:
                    break
                try:
                    text = page.extract_text()
                    if text.strip():
                        extracted_text.append(text.strip())
                except Exception as e:
                    logger.warning(f"Failed to extract text from page {i}: {e}")

            return "\n".join(extracted_text)
        except Exception as e:
            logger.error(f"Failed to extract text from PDF bytes: {e}")
            return ""

    def repair_pdf(self, input_path: Path) -> Path:
        repaired_path = Path(tempfile.mktemp(suffix="_repaired.pdf"))
        try:
            subprocess.run([
                "qpdf", "--linearize", "--ignore-xref-streams",
                str(input_path), str(repaired_path)
            ], check=True)
            logger.info(f"PDF repaired with qpdf: {repaired_path}")
            return repaired_path
        except subprocess.CalledProcessError:
            logger.warning("qpdf failed, trying Ghostscript...")
            try:
                subprocess.run([
                    "gs", "-sDEVICE=pdfwrite", "-dNOPAUSE", "-dBATCH", "-dSAFER",
                    "-sOutputFile=" + str(repaired_path),
                    str(input_path)
                ], check=True)
                logger.info(f"PDF repaired with Ghostscript: {repaired_path}")
                return repaired_path
            except subprocess.CalledProcessError as e:
                logger.error(f"Both qpdf and Ghostscript failed: {e}")
                raise

    def extract_first_n_pages_test(self, input_path: Path, n: int = 4) -> Path:
        try:
            reader = PdfReader(str(input_path))
        except Exception as e:
            logger.warning(f"Failed to read PDF {input_path}, trying repair: {e}")
            repaired = self.repair_pdf(input_path)
            reader = PdfReader(str(repaired))

        writer = PdfWriter()
        page_count = 0

        for page in reader.pages:
            try:
                if isinstance(page, NullObject):
                    logger.warning("Skipping NullObject page")
                    continue
                writer.add_page(page)
                page_count += 1
                if page_count >= n:
                    break
            except Exception as e:
                logger.warning(f"Skipping malformed page: {e}")

        if page_count == 0:
            raise ValueError("No valid pages could be extracted from the PDF")

        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=".pdf")
        with open(temp_file.name, "wb") as f:
            writer.write(f)

        return Path(temp_file.name)

    def classify_test(self, document_input: str, prompt: str, original_filename: str, is_file: bool = True, schema_type=None) -> str:
        try:
            if is_file:
                path = Path(document_input)
                if not path.exists():
                    raise FileNotFoundError(f"File not found: {document_input}")
                logger.info(f"Trimming to first 4 pages: {document_input}")
                limited_path = self.extract_first_n_pages_test(path, n=4)
                logger.info(f"Sending file to Gemini: {limited_path}")
                result = self.gemini.generate_from_file(
                    prompt=prompt,
                    file_path=limited_path,
                    schema_type=schema_type
                )
            else:
                logger.info("Sending raw text to Gemini")
                result = self.gemini.generate_from_text([prompt, document_input])

            logger.info(f"Classification result: {result}")
            return result

        except Exception as e:
            logger.error(f"Classification failed: {e}", exc_info=True)
            return "Unknown"

    def classify(self, file_bytes: bytes, prompt: str, original_filename: str, schema_type=None) -> str:
        """
        Handles classification of PDF, DOCX, image, or other file types.
        - Extracts text from DOCX (which comes as PDF bytes after conversion)
        - Sends file directly for PDFs/images
        """
        ext = Path(original_filename).suffix.lower()

        if ext == ".docx":
            try:
                # Extract text from the PDF bytes (DOCX was converted to PDF)  # TODO update it
                document_text = self.extract_text_from_pdf_bytes(file_bytes)
                if not document_text.strip():
                    logger.warning("No text extracted from DOCX document")
                    return "Unknown"

                result = self.gemini.generate_from_text(prompt=prompt, document_text=document_text)
                logger.info(f"Classification result (from DOCX text): {result}")
                return result
            except Exception as e:
                logger.error(f"Failed to extract text from DOCX: {e}", exc_info=True)
                return "Unknown"

        # Default path: save to temp file and send to Gemini
        temp_file_path = None
        try:
            if not ext:
                ext = ".bin"
            with tempfile.NamedTemporaryFile(delete=False, suffix=ext) as tmp:
                tmp.write(file_bytes)
                tmp.flush()
                temp_file_path = Path(tmp.name)

            logger.info(f"Sending file to Gemini: {original_filename} ({ext})")
            result = self.gemini.generate_from_file(
                file_path=str(temp_file_path),
                prompt=prompt,
                schema_type=schema_type
            )
            logger.info(f"Classification result: {result}")
            return result

        except Exception as e:
            logger.error(f"Failed to classify file ({original_filename}): {e}", exc_info=True)
            return "Unknown"

        finally:
            if temp_file_path and temp_file_path.exists():
                try:
                    os.remove(temp_file_path)
                    logger.debug(f"Deleted temp file: {temp_file_path}")
                except Exception as cleanup_err:
                    logger.warning(f"Failed to delete temp file {temp_file_path}: {cleanup_err}")

