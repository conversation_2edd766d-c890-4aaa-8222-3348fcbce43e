from src.classifier_service.common.classifier_node_template import make_classifier_node
from src.classifier_service.common.correction_node.self_correction_node import correction_node_with_double_check
from src.classifier_service.pacer.consts import all_categories_pacer
from src.classifier_service.pacer.prompts.base_prompt import pacer_self_correction_prompt
from src.classifier_service.pacer.prompts.prompt_factory import initial_type_classification_prompt_pacer, \
    notice_type_classification_prompt_pacer, motion_type_classification_prompt_pacer, \
    discovery_type_classification_prompt_pacer, orders_case_management_classification_prompt_pacer, \
    trial_pretrial_classification_prompt_pacer

# Initial Classifier Node

initial_type_classifier_pacer = make_classifier_node(initial_type_classification_prompt_pacer, node_name="------[PACER INITIAL_DOC_TYPE]-----")

# Notices Node
notice_type_classifier_pacer = make_classifier_node(notice_type_classification_prompt_pacer, node_name="------[PACER NOTICE_DOC_TYPE]-----")

# Motions Node
motion_type_classifier_pacer = make_classifier_node(motion_type_classification_prompt_pacer, node_name="------[PACER MOTION_DOC_TYPE]-----")

# Discovery Node
discovery_type_classifier_pacer = make_classifier_node(discovery_type_classification_prompt_pacer, node_name="------[PACER DISCOVERY_DOC_TYPE]-----")

# Orders & Case Management Node
orders_case_management_classifier_pacer = make_classifier_node(orders_case_management_classification_prompt_pacer, node_name="------[PACER ORDERS_CASE_MANAGEMENT_DOC_TYPE]-----")

# Trial & Pre-Trial Preparations Node
trial_pretrial_classifier_pacer = make_classifier_node(trial_pretrial_classification_prompt_pacer, node_name="------[PACER TRIAL_PRETRIAL_DOC_TYPE]-----")

# Self Correction Node
pacer_correction_node_node = correction_node_with_double_check(
    prompt_template=pacer_self_correction_prompt,
    categories=all_categories_pacer,
    node_name="PACER_CORRECTION_NODE"
)