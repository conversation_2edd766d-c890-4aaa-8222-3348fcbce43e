import pandas as pd

def save_misclassified_documents(results_df: pd.DataFrame, run_number: int, output_dir: str = "."):
    """
    Extracts misclassified rows from a results DataFrame and writes them to an Excel file.

    Parameters:
        results_df (pd.DataFrame): DataFrame with at least 'PDF Title', 'Predicted Label',
                                   'Actual Doc Type', and 'Correct' columns.
        run_number (int): The classification run number (used in filename).
        output_dir (str): Optional directory to save the file. Defaults to current directory.
    """
    if not {"PDF Title", "Predicted Label", "Actual Doc Type", "Correct"}.issubset(results_df.columns):
        raise ValueError("Missing required columns in results DataFrame.")

    misclassified = results_df[results_df["Correct"] == 0].copy()

    if misclassified.empty:
        print("✅ No misclassified documents.")
        return

    output_file = f"{output_dir}/misclassified_docs_run_{run_number}.xlsx"
    misclassified[["PDF Title", "Predicted Label", "Actual Doc Type"]].to_excel(output_file, index=False)
    print(f"⚠️ Misclassified documents saved to: {output_file}")
