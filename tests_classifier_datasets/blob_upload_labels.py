import os
from azure.storage.blob import BlobServiceClient, ContentSettings

# Constants
CONTAINER_NAME_TEST = "testing-datasets"
CONNECTION_STRING = "DefaultEndpointsProtocol=https;AccountName=fileflowsatest;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net"
LOCAL_EXCEL_PATH = "/Users/<USER>/Desktop/fileflow/fileflow.document-classifier/tests_classifier/efile-labeled-documents.xlsx"
BLOB_FOLDER = "labels"
EXCEL_FILENAME = os.path.basename(LOCAL_EXCEL_PATH)
BLOB_PATH = f"{BLOB_FOLDER}/{EXCEL_FILENAME}"

# Connect to Blob Storage
blob_service_client = BlobServiceClient.from_connection_string(CONNECTION_STRING)
container_client = blob_service_client.get_container_client(CONTAINER_NAME_TEST)

# Upload the Excel file
try:
    with open(LOCAL_EXCEL_PATH, "rb") as data:
        container_client.upload_blob(
            name=BLOB_PATH,
            data=data,
            overwrite=True,
            content_settings=ContentSettings(content_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
        )
    print(f"Successfully uploaded: {EXCEL_FILENAME} to folder '{BLOB_FOLDER}'")
except Exception as e:
    print(f"Failed to upload {EXCEL_FILENAME}: {e}")
