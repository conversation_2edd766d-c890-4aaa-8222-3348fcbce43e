from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
import pandas as pd
from io import BytesIO
from src.blob_reader import AzureBlobClient
from src.classifier_service.common.graph_state import DocumentState
from src.save_misclassified_docs import save_misclassified_documents

# Constants
CONNECTION_STRING = "DefaultEndpointsProtocol=https;AccountName=fileflowsatest;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net"
CONTAINER_NAME_TEST = "testing-datasets"
LABELS_BLOB_NAME = "labels/efile-labeled-documents.xlsx"
BLOB_FOLDER = "efile-dataset"
CLASSIFIER_TYPE = "efile"
CLASSIFICATION_RUN_NUMBER = 1


def run_classification(connection_string, container_name, labels_blob_name, classifier_type, blob_folder, run_number, evaluate=False):
    # Init blob client
    blob_client = AzureBlobClient(connection_string)

    # Download Excel with labels
    labels_blob = blob_client.get_excel_labels(container_name, labels_blob_name)
    labels_df = pd.read_excel(BytesIO(labels_blob), engine="openpyxl")

    assert 'PDF Title' in labels_df.columns
    if evaluate:
        assert 'Actual Doc Type' in labels_df.columns

    # Select classifier graph
    if classifier_type == "efile":
        from src.classifier_service.efile.graph.graph_nodes import efile_document_classifier_graph
        classifier_graph = efile_document_classifier_graph
    elif classifier_type == "non_court":
        from src.classifier_service.non_court_docs.graph.graph_nodes import non_court_document_classifier_graph
        classifier_graph = non_court_document_classifier_graph
    elif classifier_type == "pacer":
        from src.classifier_service.pacer.graph.graph_nodes import pacer_document_classifier_graph
        classifier_graph = pacer_document_classifier_graph
    else:
        raise ValueError(f"Invalid classifier type: {classifier_type}")

    # Classification function (to be run in threads)
    def classify_row(row):
        blob_name = f"{blob_folder}/{row['PDF Title']}"
        try:
            doc = blob_client.get_first_5_pages(container_name, blob_name)
            if not doc:
                raise Exception("Document not found or unreadable")

            initial_state = DocumentState(
                doc_title=row["PDF Title"],
                document=doc,
                classification_result=None
            )
            final_state = classifier_graph.invoke(initial_state)
            predicted_label = final_state["classification_result"]
        except Exception as e:
            predicted_label = f"ERROR: {e}"

        result = {
            "PDF Title": row["PDF Title"],
            "Predicted Label": predicted_label
        }

        if evaluate:
            actual_label = row.get("Actual Doc Type", None)
            result["Actual Doc Type"] = actual_label
            result["Correct"] = int(predicted_label == actual_label) if not str(predicted_label).startswith("ERROR") else 0

        return result

    # Run classifications concurrently using ThreadPoolExecutor
    results = []
    with ThreadPoolExecutor(max_workers=40) as executor:
        futures = [executor.submit(classify_row, row) for _, row in labels_df.iterrows()]
        for future in as_completed(futures):
            results.append(future.result())

    # Save results to Excel
    results_df = pd.DataFrame(results)
    output_filename = f"classification_run_{run_number}.xlsx"
    results_df.to_excel(output_filename, index=False)
    # Save misclassified documents
    save_misclassified_documents(results_df, run_number=CLASSIFICATION_RUN_NUMBER)
    print(f"📁 Classification results saved to: {output_filename}")

    # Run evaluation after saving
    if evaluate:
        try:
            accuracy = results_df["Correct"].mean()
            print(f"✅ Accuracy: {accuracy:.2%}")
        except Exception as e:
            print(f"⚠️ Evaluation failed: {e}")

# Example usage
if __name__ == "__main__":
    run_classification(
        CONNECTION_STRING,
        CONTAINER_NAME_TEST,
        LABELS_BLOB_NAME,
        CLASSIFIER_TYPE,
        BLOB_FOLDER,
        CLASSIFICATION_RUN_NUMBER,
        evaluate=True
    )
