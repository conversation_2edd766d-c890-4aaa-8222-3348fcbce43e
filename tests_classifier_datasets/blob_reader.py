from azure.storage.blob import BlobServiceClient
from azure.core.exceptions import ResourceNotFoundError
from PyPDF2 import PdfReader, PdfWriter
from io import BytesIO
from typing import List, Optional
import logging

logger = logging.getLogger(__name__)

class AzureBlobClient:
    def __init__(self, connection_string: str):
        self.blob_service_client = BlobServiceClient.from_connection_string(connection_string)

    def get_excel_labels(self, container_name: str, blob_name: str) -> bytes:
        """
        Download Excel file as raw bytes.
        """
        try:
            blob_client = self.blob_service_client.get_blob_client(container=container_name, blob=blob_name)
            return blob_client.download_blob().readall()
        except Exception as e:
            logger.error(f"Failed to download Excel blob '{blob_name}': {e}")
            raise

    def list_documents(self, container_name: str, folder_prefix: str) -> List[str]:
        """
        List all document blob names in a specified folder/prefix.
        """
        try:
            container_client = self.blob_service_client.get_container_client(container_name)
            return [blob.name for blob in container_client.list_blobs(name_starts_with=folder_prefix) if blob.name.endswith('.pdf')]
        except Exception as e:
            logger.error(f"Failed to list blobs with prefix '{folder_prefix}': {e}")
            raise

    def get_first_5_pages(self, container_name: str, blob_name: str) -> Optional[bytes]:
        """
        Download first 5 pages of a PDF document as bytes.
        """
        try:
            blob_client = self.blob_service_client.get_blob_client(container=container_name, blob=blob_name)
            blob_data = blob_client.download_blob().readall()
            reader = PdfReader(BytesIO(blob_data))
            writer = PdfWriter()

            for page in reader.pages[:5]:
                writer.add_page(page)

            output_stream = BytesIO()
            writer.write(output_stream)
            return output_stream.getvalue()
        except Exception as e:
            logger.error(f"Failed to process PDF '{blob_name}': {e}")
            return None
