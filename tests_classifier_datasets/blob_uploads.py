import os

from azure.storage.blob import BlobServiceClient, ContentSettings

CONTAINER_NAME_TEST = "testing-datasets"
CONNECTION_STRING = "DefaultEndpointsProtocol=https;AccountName=fileflowsatest;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net"

LOCAL_FOLDER = "/Users/<USER>/Desktop/fileflow/fileflow.document-classifier/tests_classifier/Doc Types Test"
BLOB_FOLDER = "efile-dataset"

# Connect to Blob Storage
blob_service_client = BlobServiceClient.from_connection_string(CONNECTION_STRING)
container_client = blob_service_client.get_container_client(CONTAINER_NAME_TEST)

# Counter
uploaded_count = 0

# Upload all files
for root, _, files in os.walk(LOCAL_FOLDER):
    for file_name in files:
        local_file_path = os.path.join(root, file_name)
        blob_path = f"{BLOB_FOLDER}/{file_name}"

        try:
            with open(local_file_path, "rb") as data:
                container_client.upload_blob(
                    name=blob_path,
                    data=data,
                    overwrite=True,
                    content_settings=ContentSettings(content_type="application/octet-stream")
                )
            uploaded_count += 1
            print(f"Uploaded: {file_name}")
        except Exception as e:
            print(f"Failed to upload {file_name}: {e}")

# Final summary
print(f"\nTotal documents uploaded: {uploaded_count}")