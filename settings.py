from decouple import config


STORAGE_CONNECTION_STRING = config("STORAGE_CONNECTION_STRING")

SQL_SERVER = config("SQL_SERVER")
SQL_DATABASE = config("SQL_DATABASE")
SQL_USERNAME = config("SQL_USERNAME")
SQL_PASSWORD = config("SQL_PASSWORD")


# QUEUE_NAME = config("QUEUE_NAME")
SERVICE_BUS_CONNECTION_STRING = config("SERVICE_BUS_CONNECTION_STRING")
AZURE_OPENAI_KEY = config("AZURE_OPENAI_KEY")
AZURE_OPENAI_ENDPOINT = config("AZURE_OPENAI_ENDPOINT")
DOC_INT_ENDPOINT = config("DOC_INT_ENDPOINT")



OPENAI_MODEL_ENDPOINT = config(
    "OPENAI_MODEL_ENDPOINT",
    default="https://oai.azure.com/portal/4970430dd2b843d9890ecddb95bd2d91",
)
DEPLOYMENT_NAME = config("DEPLOYMENT_NAME", default="medchron-summarization")
API_VERSION = config("API_VERSION", default="2024-08-01-preview")
AZURE_OPENAI_EMBEDDING_MODEL = config("AZURE_OPENAI_EMBEDDING_MODEL", default="ada002")
EMBEDDING_MODEL_API_VERSION = config(
    "EMBEDDING_MODEL_API_VERSION", default="2023-05-15"
)
AZURE_OPEN_AI_MODEL_DIMENSIONS = config(
    "AZURE_OPEN_AI_MODEL_DIMENSIONS", default="1536"
)
DEPLOYMENT_NAME_GPT_4 = config("DEPLOYMENT_NAME_GPT_4")

APIM_KEY = config("APIM_KEY")
APIM_ENDPOINT = config("APIM_ENDPOINT")
GEMINI_API_KEY = config("GEMINI_API_KEY")
GEMINI_MODEL_NAME = config("GEMINI_MODEL_NAME", default="gemini-2.5-flash-preview-04-17")
GEMINI_MODEL_NAME_FINAL_CLASSIFIER= config("GEMINI_MODEL_NAME_FINAL_CLASSIFIER", default="gemini-2.5-flash-preview-04-17")

